/**
 * 📝 PROPERTY RESPONSE FORMATTER
 * 
 * Formats property data responses into conversational, user-friendly answers
 * - Converts raw ArcGIS data into natural language
 * - Provides context and explanations
 * - Maintains consistent response format
 */

import { logger } from '../utils/logger.js';
import { PropertyData } from './smartPropertyAgent.js';

export interface FormattedPropertyResponse {
  answer: string;
  sources: string[];
  confidence: number;
}

export class PropertyResponseFormatter {

  /**
   * Format property data into a conversational response
   */
  async formatPropertyResponse(
    propertyData: PropertyData,
    jurisdiction: string,
    originalQuery: string,
    queryType: string
  ): Promise<FormattedPropertyResponse> {
    
    try {
      logger.info(`📝 Formatting property response for ${jurisdiction}: ${queryType}`);

      const sources: string[] = [];
      let answer = '';
      let confidence = 0.8; // Base confidence for successful ArcGIS queries

      // Format based on what data we have
      if (queryType === 'both' && propertyData.zoning && propertyData.flu) {
        answer = this.formatBothResponse(propertyData, jurisdiction, originalQuery);
        sources.push(propertyData.zoning.source, propertyData.flu.source);
        confidence = 0.95; // High confidence when we have both data types
        
      } else if (queryType === 'zoning' && propertyData.zoning) {
        answer = this.formatZoningResponse(propertyData.zoning, jurisdiction, originalQuery);
        sources.push(propertyData.zoning.source);
        confidence = 0.9;
        
      } else if (queryType === 'flu' && propertyData.flu) {
        answer = this.formatFLUResponse(propertyData.flu, jurisdiction, originalQuery);
        sources.push(propertyData.flu.source);
        confidence = 0.9;
        
      } else if (propertyData.zoning) {
        // Fallback to zoning if we have it but not what was requested
        answer = this.formatZoningResponse(propertyData.zoning, jurisdiction, originalQuery);
        sources.push(propertyData.zoning.source);
        confidence = 0.75; // Lower confidence since it's not exactly what was requested
        
      } else if (propertyData.flu) {
        // Fallback to FLU if we have it but not what was requested
        answer = this.formatFLUResponse(propertyData.flu, jurisdiction, originalQuery);
        sources.push(propertyData.flu.source);
        confidence = 0.75;
        
      } else {
        throw new Error('No property data available to format');
      }

      // Add helpful footer information
      answer += this.addFooterInfo(jurisdiction);

      // Remove duplicate sources
      const uniqueSources = [...new Set(sources)];

      logger.info(`📝 Formatted response with confidence ${confidence}`);

      return {
        answer,
        sources: uniqueSources,
        confidence
      };

    } catch (error) {
      logger.error('Error formatting property response:', error);
      throw error;
    }
  }

  /**
   * Format response when we have both zoning and FLU data
   */
  private formatBothResponse(propertyData: PropertyData, jurisdiction: string, query: string): string {
    const zoning = propertyData.zoning!;
    const flu = propertyData.flu!;

    return `Based on the official GIS data for ${jurisdiction}, here's what I found:

**Zoning:** The property is zoned as **${zoning.value}** (${zoning.description}). This zoning designation determines what types of buildings and uses are allowed on the property.

**Future Land Use:** The comprehensive plan designates this area as **${flu.value}** (${flu.description}). This represents the long-term planning vision for how this area should develop.

Both the current zoning and future land use planning are important factors to consider for any development or use of this property. The zoning provides the immediate legal framework, while the future land use designation shows the community's long-term vision for the area.`;
  }

  /**
   * Format zoning-only response
   */
  private formatZoningResponse(zoning: PropertyData['zoning'], jurisdiction: string, query: string): string {
    // Check if this is state database data
    const isStateData = zoning!.source && (
      zoning!.source.includes('Future Land Use Database') ||
      zoning!.source.includes('(FL)') ||
      zoning!.source.includes('(CA)') ||
      zoning!.source.includes('(TX)')
    );

    if (isStateData) {
      return this.formatStateLandUseResponse(zoning!, jurisdiction, query, 'zoning');
    }

    return `According to the official zoning data for ${jurisdiction}, this property is zoned as **${zoning!.value}**.

${zoning!.description}. This zoning designation determines what types of buildings, structures, and uses are permitted on the property.

Zoning regulations typically cover aspects like:
- Permitted and prohibited uses
- Building height and size restrictions
- Setback requirements from property lines
- Parking and landscaping requirements
- Density limitations

For specific details about what's allowed under this zoning classification, you may want to review the complete zoning ordinance or contact the local planning department.`;
  }

  /**
   * Format FLU-only response
   */
  private formatFLUResponse(flu: PropertyData['flu'], jurisdiction: string, query: string): string {
    return `According to the comprehensive plan for ${jurisdiction}, this property is designated for **${flu!.value}** future land use.

${flu!.description}. This designation represents the community's long-term vision for how this area should develop over time.

Future Land Use designations guide:
- Long-term development patterns
- Infrastructure planning and investment
- Zoning decisions and amendments
- Community growth management
- Transportation and utility planning

While the Future Land Use designation provides important guidance, the current zoning regulations determine what's immediately permitted on the property. For development questions, you'll want to check both the current zoning and this future land use designation.`;
  }

  /**
   * Add helpful footer information
   */
  private addFooterInfo(jurisdiction: string): string {
    return `

---

**Need More Information?**
For detailed regulations, permit requirements, or specific development questions, I recommend contacting the ${jurisdiction} Planning Department directly. They can provide the most current information about local requirements and help with any applications or permits you might need.

*This information is based on official GIS data and is current as of the time of this query.*`;
  }

  /**
   * Format error response when property data is not available
   */
  formatUnavailableResponse(jurisdiction: string, queryType: string): FormattedPropertyResponse {
    const typeDescription = {
      'zoning': 'zoning information',
      'flu': 'future land use information',
      'both': 'zoning and future land use information'
    };

    const answer = `I don't currently have automated access to ${typeDescription[queryType as keyof typeof typeDescription]} for ${jurisdiction} through our GIS system.

However, this information is typically available through the local planning department. Here are some ways you can get this information:

**Contact the Planning Department:**
- Visit the ${jurisdiction} city/county website
- Call the planning or zoning department directly
- Visit their office in person for maps and documents

**Online Resources:**
- Many jurisdictions have online zoning maps
- Comprehensive plans are often available as PDFs
- Some areas have interactive GIS portals for public use

**Professional Help:**
- Local surveyors often have access to detailed zoning information
- Real estate professionals familiar with the area
- Land use attorneys for complex questions

I apologize that I couldn't provide the specific information you're looking for, but the local planning department will be your best resource for accurate, up-to-date ${typeDescription[queryType as keyof typeof typeDescription]}.`;

    return {
      answer,
      sources: [],
      confidence: 0.0
    };
  }

  /**
   * Format state land use response (for state database data)
   */
  private formatStateLandUseResponse(
    data: { value: string; description: string; source: string },
    jurisdiction: string,
    query: string,
    type: 'zoning' | 'flu'
  ): string {
    const isFloridaData = data.source.includes('(FL)');
    const stateName = isFloridaData ? 'Florida' : 'the state';

    // Determine if user is asking about specific development types
    const queryLower = query.toLowerCase();
    const isDuplexQuery = queryLower.includes('duplex');
    const isCommercialQuery = queryLower.includes('commercial') || queryLower.includes('business');
    const isAgricultureQuery = queryLower.includes('agriculture') || queryLower.includes('farm');

    let response = `Based on ${stateName}'s official property database, this property is designated as **${data.description}** (${data.value}).`;

    // Add specific guidance based on land use type
    const landUse = data.description.toUpperCase();

    if (landUse.includes('RESIDENTIAL')) {
      response += `\n\nThis residential designation typically allows for housing development. `;
      if (isDuplexQuery) {
        response += `**For duplex development:** This residential designation may allow duplexes, but you'll need to check local zoning ordinances for specific requirements like lot size, setbacks, and multi-family permissions.`;
      } else {
        response += `The specific types of residential development allowed (single-family, multi-family, etc.) depend on local zoning regulations.`;
      }
    } else if (landUse.includes('COMMERCIAL')) {
      response += `\n\nThis commercial designation typically allows for business and retail uses. `;
      if (isDuplexQuery) {
        response += `**For duplex development:** Commercial areas typically do not allow residential duplexes. You would likely need to rezone to residential or mixed-use.`;
      } else if (isCommercialQuery) {
        response += `This appears to be well-suited for your commercial development plans.`;
      } else {
        response += `Specific permitted commercial uses depend on local zoning ordinances.`;
      }
    } else if (landUse.includes('AGRICULTURE')) {
      response += `\n\nThis agricultural designation typically allows for farming and related agricultural activities. `;
      if (isDuplexQuery) {
        response += `**For duplex development:** Agricultural land typically does not allow residential duplexes. You would need to apply for rezoning to residential use, which may face restrictions depending on local growth management policies.`;
      } else if (isAgricultureQuery) {
        response += `This property appears to be appropriate for agricultural use.`;
      } else if (isCommercialQuery) {
        response += `Converting agricultural land to commercial use typically requires rezoning and may face restrictions.`;
      }
    } else if (landUse.includes('WATER')) {
      response += `\n\nThis property is designated as a water body or water area. `;
      if (isDuplexQuery || isCommercialQuery) {
        response += `**You cannot build a duplex or any structures on water-designated areas.** This designation indicates the property is a water body, wetland, or flood-prone area where construction is prohibited.`;
      } else {
        response += `Water designations typically prohibit all development to protect water resources and prevent flood damage.`;
      }
    } else if (landUse.includes('MIXED USE')) {
      response += `\n\nMixed use designation typically allows for a combination of residential, commercial, and sometimes office uses. `;
      if (isDuplexQuery) {
        response += `**For duplex development:** Mixed-use areas often allow residential duplexes as part of their flexible development options. Check local zoning for specific requirements.`;
      } else {
        response += `This provides flexibility for various development types.`;
      }
    } else if (landUse.includes('INDUSTRIAL')) {
      response += `\n\nThis industrial designation typically allows for manufacturing, warehousing, and related industrial activities. `;
      if (isDuplexQuery) {
        response += `**For duplex development:** Industrial areas typically do not allow residential duplexes. You would need to rezone to residential or mixed-use.`;
      }
    } else if (landUse.includes('CONSERVATION')) {
      response += `\n\nThis conservation designation typically restricts development to protect natural resources and environmental features. `;
      if (isDuplexQuery || isCommercialQuery) {
        response += `**Development is typically prohibited or severely restricted** in conservation areas to protect environmental resources.`;
      }
    }

    response += `\n\n**Important:** This data represents the comprehensive plan designation. Local zoning ordinances provide the specific regulations for what can be built. You should verify current zoning and any recent changes with the local planning department before proceeding with development plans.`;

    return response;
  }

  /**
   * Calculate confidence based on data quality
   */
  private calculateConfidence(propertyData: PropertyData, queryType: string): number {
    let confidence = 0.5; // Base confidence

    // Check if we have the requested data type
    if (queryType === 'zoning' && propertyData.zoning) confidence += 0.3;
    if (queryType === 'flu' && propertyData.flu) confidence += 0.3;
    if (queryType === 'both' && propertyData.zoning && propertyData.flu) confidence += 0.4;

    // Bonus for having both types regardless of what was requested
    if (propertyData.zoning && propertyData.flu) confidence += 0.1;

    // Check data quality
    if (propertyData.zoning?.value && propertyData.zoning.value.length > 2) confidence += 0.05;
    if (propertyData.flu?.value && propertyData.flu.value.length > 2) confidence += 0.05;

    // Check if we have descriptions
    if (propertyData.zoning?.description) confidence += 0.05;
    if (propertyData.flu?.description) confidence += 0.05;

    return Math.min(confidence, 1.0);
  }

  /**
   * Clean and normalize property values
   */
  private cleanPropertyValue(value: string): string {
    if (!value) return 'Unknown';
    
    // Remove common prefixes/suffixes that might be in the data
    let cleaned = value.toString().trim();
    
    // Handle null/undefined values that might come as strings
    if (cleaned.toLowerCase() === 'null' || cleaned.toLowerCase() === 'undefined') {
      return 'Unknown';
    }
    
    // Capitalize first letter if it's all lowercase
    if (cleaned === cleaned.toLowerCase()) {
      cleaned = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);
    }
    
    return cleaned;
  }

  /**
   * Generate a fallback description for unknown codes
   */
  private generateFallbackDescription(value: string, type: 'zoning' | 'flu'): string {
    const cleanValue = this.cleanPropertyValue(value);
    
    if (type === 'zoning') {
      return `This property is zoned as ${cleanValue}. Contact the local planning department for specific details about permitted uses and restrictions under this zoning classification.`;
    } else {
      return `This area is designated for ${cleanValue} in the comprehensive plan. Contact the planning department for more details about this future land use designation.`;
    }
  }
}
