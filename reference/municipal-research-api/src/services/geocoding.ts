import axios from 'axios';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';

export interface GeocodeResult {
  formattedAddress: string;
  jurisdiction: string;
  city: string;
  state: string;
  county: string;
  zipCode: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

class GeocodingService {
  private apiKey: string;

  constructor() {
    this.apiKey = config.google.geocodingApiKey;
  }

  /**
   * Get jurisdiction information from an address
   */
  async getJurisdiction(address: string): Promise<string> {
    try {
      logger.info(`🌍 Geocoding address: ${address}`);

      const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
        params: {
          address,
          key: this.apiKey
        },
        timeout: 10000
      });

      if (response.data.status !== 'OK' || !response.data.results.length) {
        throw new Error(`Geocoding failed: ${response.data.status}`);
      }

      const result = response.data.results[0];
      const components = result.address_components;

      // Extract address components
      let city = '';
      let state = '';
      let county = '';
      let zipCode = '';

      for (const component of components) {
        const types = component.types;
        
        if (types.includes('locality')) {
          city = component.long_name;
        } else if (types.includes('administrative_area_level_3')) {
          // Township level
          city = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          state = component.short_name;
        } else if (types.includes('administrative_area_level_2')) {
          county = component.long_name;
        } else if (types.includes('postal_code')) {
          zipCode = component.long_name;
        }
      }

      // Determine jurisdiction (city vs township vs county)
      const jurisdiction = this.determineJurisdiction(city, county, state, components);
      
      logger.info(`🏛️ Jurisdiction determined: ${jurisdiction}`);
      return jurisdiction;

    } catch (error) {
      logger.error('Geocoding error:', error);
      throw new Error(`Failed to determine jurisdiction: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get detailed geocoding information
   */
  async getDetailedGeocode(address: string): Promise<GeocodeResult> {
    try {
      const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
        params: {
          address,
          key: this.apiKey
        },
        timeout: 10000
      });

      if (response.data.status !== 'OK' || !response.data.results.length) {
        throw new Error(`Geocoding failed: ${response.data.status}`);
      }

      const result = response.data.results[0];
      const components = result.address_components;
      const location = result.geometry.location;

      // Extract components
      let city = '';
      let state = '';
      let county = '';
      let zipCode = '';

      for (const component of components) {
        const types = component.types;
        
        if (types.includes('locality')) {
          city = component.long_name;
        } else if (types.includes('administrative_area_level_3')) {
          city = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          state = component.short_name;
        } else if (types.includes('administrative_area_level_2')) {
          county = component.long_name;
        } else if (types.includes('postal_code')) {
          zipCode = component.long_name;
        }
      }

      const jurisdiction = this.determineJurisdiction(city, county, state, components);

      return {
        formattedAddress: result.formatted_address,
        jurisdiction,
        city,
        state,
        county,
        zipCode,
        coordinates: {
          lat: location.lat,
          lng: location.lng
        }
      };

    } catch (error) {
      logger.error('Detailed geocoding error:', error);
      throw new Error(`Failed to geocode address: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Determine the appropriate jurisdiction for municipal research
   */
  private determineJurisdiction(city: string, county: string, state: string, components: any[]): string {
    // Check for township
    const township = components.find(c => 
      c.types.includes('administrative_area_level_3') && 
      c.long_name.toLowerCase().includes('township')
    );

    if (township) {
      return `${township.long_name}, ${state}`;
    }

    // Check for city
    if (city) {
      return `${city}, ${state}`;
    }

    // Fall back to county
    if (county) {
      return `${county}, ${state}`;
    }

    // Last resort - use state
    return state;
  }

  /**
   * Validate that an address can be geocoded
   */
  async validateAddress(address: string): Promise<boolean> {
    try {
      await this.getJurisdiction(address);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get multiple jurisdiction suggestions for ambiguous addresses
   */
  async getJurisdictionSuggestions(address: string): Promise<string[]> {
    try {
      const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
        params: {
          address,
          key: this.apiKey
        },
        timeout: 10000
      });

      if (response.data.status !== 'OK') {
        return [];
      }

      const jurisdictions: string[] = response.data.results.map((result: any) => {
        const components = result.address_components;
        let city = '';
        let state = '';
        let county = '';

        for (const component of components) {
          const types = component.types;
          
          if (types.includes('locality')) {
            city = component.long_name;
          } else if (types.includes('administrative_area_level_3')) {
            city = component.long_name;
          } else if (types.includes('administrative_area_level_1')) {
            state = component.short_name;
          } else if (types.includes('administrative_area_level_2')) {
            county = component.long_name;
          }
        }

        return this.determineJurisdiction(city, county, state, components);
      });

      // Remove duplicates and return
      return [...new Set(jurisdictions)];

    } catch (error) {
      logger.error('Jurisdiction suggestions error:', error);
      return [];
    }
  }
}

export const geocodingService = new GeocodingService();
