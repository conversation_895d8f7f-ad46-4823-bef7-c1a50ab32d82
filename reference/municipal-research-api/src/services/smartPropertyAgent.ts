/**
 * 🏛️ SMART PROPERTY AGENT
 * 
 * Intelligent agent for property-related queries (zoning, FLU, etc.)
 * - Detects property queries using keyword analysis
 * - Maps jurisdictions to ArcGIS endpoints
 * - Queries ArcGIS REST APIs directly
 * - Returns standardized property data responses
 * - Falls back to Perplexity for unsupported queries
 */

import { createClient } from '@supabase/supabase-js';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';
import { PropertyQueryAnalyzer } from './propertyQueryAnalyzer.js';
import { ArcGISClient } from './arcgisClient.js';
import { PropertyResponseFormatter } from './propertyResponseFormatter.js';
import { StatePropertyService } from './statePropertyService.js';
import { geocodingService } from './geocoding.js';
import crypto from 'crypto';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

export interface SmartAgentRequest {
  address: string;
  query: string;
  userId?: string;
  apiKeyId?: string;
}

export interface SmartAgentResult {
  canHandle: boolean;
  response?: {
    success: boolean;
    jurisdiction: string;
    topic: string;
    answer: string;
    sources: string[];
    confidence: number;
    cached: boolean;
    processingTimeMs: number;
    costUsd: number;
    method: 'smart-agent' | 'cache';
  };
  fallbackReason?: string;
}

export interface JurisdictionGISConfig {
  id: string;
  jurisdiction_name: string;
  state_code: string;
  arcgis_base_url: string;
  zoning_service_url?: string;
  zoning_layer_id?: number;
  zoning_field_name?: string;
  flu_service_url?: string;
  flu_layer_id?: number;
  flu_field_name?: string;
  coordinate_system: string;
  is_active: boolean;
}

export interface PropertyData {
  zoning?: {
    value: string;
    description: string;
    source: string;
  };
  flu?: {
    value: string;
    description: string;
    source: string;
  };
}

class SmartPropertyAgent {
  private queryAnalyzer: PropertyQueryAnalyzer;
  private arcgisClient: ArcGISClient;
  private responseFormatter: PropertyResponseFormatter;
  private statePropertyService: StatePropertyService;

  constructor() {
    this.queryAnalyzer = new PropertyQueryAnalyzer();
    this.arcgisClient = new ArcGISClient();
    this.responseFormatter = new PropertyResponseFormatter();
    this.statePropertyService = new StatePropertyService();
  }

  /**
   * Main entry point - determines if agent can handle query and processes it
   */
  async tryHandleQuery(request: SmartAgentRequest): Promise<SmartAgentResult> {
    const startTime = Date.now();
    
    try {
      logger.info(`🤖 Smart Agent analyzing: "${request.query}" for ${request.address}`);

      // Step 1: Analyze query to see if it's property-related
      const queryAnalysis = await this.queryAnalyzer.analyzeQuery(request.query);
      
      if (!queryAnalysis.canHandle) {
        logger.info(`🤖 Smart Agent cannot handle query: ${queryAnalysis.reason}`);
        return {
          canHandle: false,
          fallbackReason: queryAnalysis.reason
        };
      }

      logger.info(`🤖 Smart Agent can handle query: ${queryAnalysis.queryType} (confidence: ${queryAnalysis.confidence})`);

      // Step 2: Get jurisdiction from address
      const jurisdiction = await geocodingService.getJurisdiction(request.address);
      logger.info(`🏛️ Identified jurisdiction: ${jurisdiction}`);

      // Step 3: Check cache first
      const cachedResult = await this.getCachedPropertyData(request.address, jurisdiction, queryAnalysis.queryType);
      if (cachedResult) {
        logger.info(`✅ Cache hit for ${queryAnalysis.queryType} in ${jurisdiction}`);
        return {
          canHandle: true,
          response: {
            success: true,
            jurisdiction,
            topic: queryAnalysis.queryType,
            answer: cachedResult.answer,
            sources: cachedResult.sources,
            confidence: cachedResult.confidence,
            cached: true,
            processingTimeMs: Date.now() - startTime,
            costUsd: 0,
            method: 'cache'
          }
        };
      }

      // Step 4: Get GIS configuration for jurisdiction
      const gisConfig = await this.getJurisdictionGISConfig(jurisdiction);
      if (!gisConfig) {
        logger.info(`🤖 No GIS configuration found for ${jurisdiction}`);
        return {
          canHandle: false,
          fallbackReason: `GIS data not available for ${jurisdiction}`
        };
      }

      // Step 5: Get coordinates for address
      const geocodeResult = await geocodingService.getDetailedGeocode(request.address);
      if (!geocodeResult || !geocodeResult.coordinates) {
        logger.error(`❌ Could not geocode address: ${request.address}`);
        return {
          canHandle: false,
          fallbackReason: 'Could not determine coordinates for address'
        };
      }
      const coordinates = geocodeResult.coordinates;

      // Step 6: Try state-based property data first
      let propertyData: PropertyData | null = null;

      if (this.statePropertyService.canHandleLocation(request.address, coordinates)) {
        logger.info(`🗺️ Trying state property service for ${request.address}`);
        propertyData = await this.queryStatePropertyData(coordinates, request.address, queryAnalysis.queryType);
      }

      // Step 7: Fallback to ArcGIS if no state data found
      if (!propertyData || Object.keys(propertyData).length === 0) {
        logger.info(`🗺️ Trying ArcGIS fallback for ${request.address}`);
        propertyData = await this.queryPropertyData(
          coordinates,
          gisConfig,
          queryAnalysis.queryType
        );
      }

      if (!propertyData || Object.keys(propertyData).length === 0) {
        logger.info(`🤖 No property data found for ${request.address}`);
        return {
          canHandle: false,
          fallbackReason: 'No property data found at this location'
        };
      }

      // Step 8: Format response
      const formattedResponse = await this.responseFormatter.formatPropertyResponse(
        propertyData,
        jurisdiction,
        request.query,
        queryAnalysis.queryType
      );

      // Step 9: Cache the result
      await this.cachePropertyData(
        request.address,
        jurisdiction,
        queryAnalysis.queryType,
        formattedResponse
      );

      const totalTime = Date.now() - startTime;
      logger.info(`✅ Smart Agent completed in ${totalTime}ms with confidence ${formattedResponse.confidence}`);

      return {
        canHandle: true,
        response: {
          success: true,
          jurisdiction,
          topic: queryAnalysis.queryType,
          answer: formattedResponse.answer,
          sources: formattedResponse.sources,
          confidence: formattedResponse.confidence,
          cached: false,
          processingTimeMs: totalTime,
          costUsd: 0, // ArcGIS queries are free
          method: 'smart-agent'
        }
      };

    } catch (error) {
      logger.error('Smart Agent error:', error);
      return {
        canHandle: false,
        fallbackReason: `Smart Agent error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * Get GIS configuration for a jurisdiction
   */
  private async getJurisdictionGISConfig(jurisdiction: string): Promise<JurisdictionGISConfig | null> {
    try {
      // Parse jurisdiction to extract city and state
      const parts = jurisdiction.split(',').map(p => p.trim());
      if (parts.length < 2) {
        logger.error(`Invalid jurisdiction format: ${jurisdiction}`);
        return null;
      }

      const cityName = parts[0];
      const stateCode = parts[1];

      const { data, error } = await supabase
        .rpc('get_jurisdiction_gis_config', {
          jurisdiction_name_param: cityName,
          state_code_param: stateCode
        });

      if (error) {
        logger.error('Database error getting GIS config:', error);
        return null;
      }

      return data && data.length > 0 ? data[0] : null;

    } catch (error) {
      logger.error('Error getting jurisdiction GIS config:', error);
      return null;
    }
  }

  /**
   * Query ArcGIS for property data
   */
  private async queryPropertyData(
    coordinates: { lat: number; lng: number },
    gisConfig: JurisdictionGISConfig,
    queryType: string
  ): Promise<PropertyData | null> {
    try {
      const propertyData: PropertyData = {};

      // Query zoning data if requested and available
      if ((queryType === 'zoning' || queryType === 'both') && 
          gisConfig.zoning_service_url && 
          gisConfig.zoning_field_name) {
        
        const zoningData = await this.arcgisClient.queryPoint(
          gisConfig.zoning_service_url,
          gisConfig.zoning_layer_id || 0,
          coordinates,
          [gisConfig.zoning_field_name]
        );

        if (zoningData && zoningData[gisConfig.zoning_field_name]) {
          propertyData.zoning = {
            value: zoningData[gisConfig.zoning_field_name],
            description: this.getZoningDescription(zoningData[gisConfig.zoning_field_name]),
            source: gisConfig.zoning_service_url
          };
        }
      }

      // Query FLU data if requested and available
      if ((queryType === 'flu' || queryType === 'both') && 
          gisConfig.flu_service_url && 
          gisConfig.flu_field_name) {
        
        const fluData = await this.arcgisClient.queryPoint(
          gisConfig.flu_service_url,
          gisConfig.flu_layer_id || 0,
          coordinates,
          [gisConfig.flu_field_name]
        );

        if (fluData && fluData[gisConfig.flu_field_name]) {
          propertyData.flu = {
            value: fluData[gisConfig.flu_field_name],
            description: this.getFLUDescription(fluData[gisConfig.flu_field_name]),
            source: gisConfig.flu_service_url
          };
        }
      }

      return Object.keys(propertyData).length > 0 ? propertyData : null;

    } catch (error) {
      logger.error('Error querying property data:', error);
      return null;
    }
  }

  /**
   * Generate address hash for caching
   */
  private generateAddressHash(address: string): string {
    return crypto.createHash('md5').update(address.toLowerCase().trim()).digest('hex');
  }

  /**
   * Cache property data
   */
  private async cachePropertyData(
    address: string,
    jurisdiction: string,
    queryType: string,
    response: any
  ): Promise<void> {
    try {
      const addressHash = this.generateAddressHash(address);
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      await supabase
        .from('property_data_cache')
        .upsert({
          address_hash: addressHash,
          jurisdiction_name: jurisdiction,
          query_type: queryType,
          response_data: response,
          expires_at: expiresAt.toISOString()
        });

    } catch (error) {
      logger.error('Error caching property data:', error);
      // Don't throw - caching failure shouldn't break the response
    }
  }

  /**
   * Get cached property data
   */
  private async getCachedPropertyData(
    address: string,
    jurisdiction: string,
    queryType: string
  ): Promise<any | null> {
    try {
      const addressHash = this.generateAddressHash(address);

      const { data, error } = await supabase
        .from('property_data_cache')
        .select('response_data')
        .eq('address_hash', addressHash)
        .eq('jurisdiction_name', jurisdiction)
        .eq('query_type', queryType)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !data) {
        return null;
      }

      return data.response_data;

    } catch (error) {
      logger.error('Error getting cached property data:', error);
      return null;
    }
  }

  /**
   * Query state property data from Supabase
   */
  private async queryStatePropertyData(
    coordinates: { lat: number; lng: number },
    address: string,
    queryType: string
  ): Promise<PropertyData | null> {
    try {
      logger.info(`🗺️ Querying state property data for ${address}`);

      // Try querying by address first
      const result = await this.statePropertyService.queryByAddress(address);

      if (!result || !result.found || result.properties.length === 0) {
        logger.info(`🗺️ No state property data found for ${address}`);
        return null;
      }

      // Use the first property found
      const property = result.properties[0];
      const propertyData: PropertyData = {};

      // Map state land use data to our PropertyData format
      if (queryType === 'flu' || queryType === 'both') {
        propertyData.flu = {
          value: property.land_use_code,
          description: property.primary_land_use,
          source: `${result.dataSource} (${property.state})`
        };
      }

      // For zoning queries, use the land use as zoning info
      if (queryType === 'zoning' || queryType === 'both') {
        propertyData.zoning = {
          value: property.land_use_code,
          description: property.primary_land_use,
          source: `${result.dataSource} (${property.state})`
        };
      }

      logger.info(`🗺️ Found ${property.state} property data: ${property.primary_land_use} (${property.land_use_code})`);
      return propertyData;

    } catch (error) {
      logger.error('Error querying state property data:', error);
      return null;
    }
  }

  /**
   * Get basic zoning description
   */
  private getZoningDescription(zoningCode: string): string {
    // Basic zoning descriptions - can be enhanced with a lookup table
    const descriptions: { [key: string]: string } = {
      'R1': 'Single-family residential',
      'R2': 'Two-family residential',
      'R3': 'Multi-family residential',
      'C1': 'Local commercial',
      'C2': 'General commercial',
      'M1': 'Light manufacturing',
      'M2': 'Heavy manufacturing'
    };

    return descriptions[zoningCode] || `Zoned as ${zoningCode}`;
  }

  /**
   * Get basic FLU description
   */
  private getFLUDescription(fluCode: string): string {
    // Basic FLU descriptions - can be enhanced with a lookup table
    const descriptions: { [key: string]: string } = {
      'LDR': 'Low density residential',
      'MDR': 'Medium density residential',
      'HDR': 'High density residential',
      'COM': 'Commercial',
      'IND': 'Industrial',
      'MU': 'Mixed use'
    };

    return descriptions[fluCode] || `Future land use: ${fluCode}`;
  }
}

export const smartPropertyAgent = new SmartPropertyAgent();
